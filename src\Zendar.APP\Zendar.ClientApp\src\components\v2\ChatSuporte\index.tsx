import { Box, Image } from '@chakra-ui/react';
import { motion } from 'framer-motion';

import { useAtendimentoChatStore } from 'store/Chat';

export const BotaoFlutuanteChat = () => {
  const { abrirAbaChat, exibirAbaChat, chatFechado } =
    useAtendimentoChatStore();

  const MotionDiv = motion.div;

  return (
    <>
      {!chatFechado && (
        <Box
          as={MotionDiv}
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          position="fixed"
          display={exibirAbaChat ? 'none' : 'block'}
          bottom="65px"
          right="25px"
          w="64px"
          h="64px"
          borderRadius="full"
          bg="linear-gradient(180deg, #96CD00 0%, #126559 100%)"
          zIndex={9999999}
          cursor="pointer"
          shadow="lg"
          onClick={abrirAbaChat}
        >
          <Image
            src="https://altuclients.s3.sa-east-1.amazonaws.com/smarkio/assets/overlay-assets/comments.svg"
            alt="Chat"
            position="absolute"
            left="15px"
            top="14px"
            w="35px"
            transition="opacity 0.4s"
          />
        </Box>
      )}
    </>
  );
};
