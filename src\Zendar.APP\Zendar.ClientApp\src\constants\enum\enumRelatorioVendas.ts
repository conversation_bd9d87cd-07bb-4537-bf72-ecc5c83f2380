import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import { validarListaServicos } from 'helpers/validation/validarListaServicos';
import enumReferenciaServicoStargate from './referenciaServicoStargate';

const possuiServicoFrenteCaixa = validarListaServicos([
  enumReferenciaServicoStargate.DISPOSITIVO_FRENTE_CAIXA,
  enumReferenciaServicoStargate.MODULO_FRENTE_CAIXA,
]);

export const EnumRelatorioVendas = {
  VENDAS_FORMA_RECEBIMENTO: 0,
  VENDAS_POR_DIA: 1,
  VENDAS_SIMPLIFICADAS: 2,
  G<PERSON><PERSON>CO_VENDAS_TOTALIZADAS_VENDEDORES: 4,
  VENDAS_TOTALIZADAS_PRODUTOS: 5,

  DETALHAMENTO_ENTREGAS_ENTREGADOR: 6,
  RESUMO_ENTREGAS_ENTREGADOR: 7,
  VAL<PERSON><PERSON>_ADICIONAIS_COBRADOS_VENDAS: 8,
  VENDAS_TOTALIZADAS_VENDEDORES: 9,
  LUCRO_AGRUPADO_POR_DIA: 10,
  TOTALIZACAO_VENDAS_PRODUTO: 11,

  properties: [
    { label: 'Vendas por forma de recebimento', value: 0 },
    {
      label: 'Vendas por dia',
      value: 1,
    },
    {
      label: 'Vendas simplificadas',
      value: 2,
    },
    {
      label: 'Gráfico de vendas totalizadas por vendedores',
      value: 4,
    },
    {
      label: 'Vendas totalizadas por produtos',
      value: 5,
    },

    {
      label: 'Detalhamento de entregas por entregador',
      value: 6,
      exibir: possuiServicoFrenteCaixa,
    },
    {
      label: 'Resumo de entregas por entregador',
      value: 7,
      exibir: possuiServicoFrenteCaixa,
    },
    {
      label: 'Valores adicionais cobrados nas vendas',
      value: 8,
      exibir: possuiServicoFrenteCaixa,
    },
    {
      label: 'Vendas totalizadas por vendedores',
      value: 9,
      exibir: possuiServicoFrenteCaixa,
    },
    {
      label: 'Lucro agrupado por dia',
      value: 10,
    },
    {
      label: 'Totalização de vendas por produto simples',
      value: 11,
    },
  ],
};

export const EnumApisRelatorioVendas = {
  [EnumRelatorioVendas.VENDAS_FORMA_RECEBIMENTO]:
    ConstanteEnderecoWebservice.RELATORIO_VENDAS_POR_FORMA_RECEBIMENTO,
  [EnumRelatorioVendas.VENDAS_POR_DIA]:
    ConstanteEnderecoWebservice.RELATORIO_VENDAS_POR_DIA,
  [EnumRelatorioVendas.VENDAS_SIMPLIFICADAS]:
    ConstanteEnderecoWebservice.RELATORIO_VENDAS_SIMPLIFICADAS,
  [EnumRelatorioVendas.GRAFICO_VENDAS_TOTALIZADAS_VENDEDORES]:
    ConstanteEnderecoWebservice.RELATORIO_GRAFICO_VENDAS_TOTALIZADAS_VENDEDORES,
  [EnumRelatorioVendas.VENDAS_TOTALIZADAS_PRODUTOS]:
    ConstanteEnderecoWebservice.RELATORIO_VENDAS_TOTALIZADAS_PRODUTOS,
  [EnumRelatorioVendas.LUCRO_AGRUPADO_POR_DIA]:
    ConstanteEnderecoWebservice.RELATORIO_VENDAS_LUCRO_AGRUPADO_POR_DIA,
  [EnumRelatorioVendas.TOTALIZACAO_VENDAS_PRODUTO]:
    ConstanteEnderecoWebservice.RELATORIO_TOTALIZACAO_VENDAS_PRODUTO,

  // As opções abaixo só são exibidas caso possuir integração Frente de Caixa
  [EnumRelatorioVendas.DETALHAMENTO_ENTREGAS_ENTREGADOR]:
    ConstanteEnderecoWebservice.RELATORIO_DETALHAMENTO_ENTREGAS_ENTREGADOR,
  [EnumRelatorioVendas.RESUMO_ENTREGAS_ENTREGADOR]:
    ConstanteEnderecoWebservice.RELATORIO_RESUMO_ENTREGAS_ENTREGADOR,
  [EnumRelatorioVendas.VALORES_ADICIONAIS_COBRADOS_VENDAS]:
    ConstanteEnderecoWebservice.RELATORIO_VALORES_ADICIONAIS_COBRADOS_VENDAS,
  [EnumRelatorioVendas.VENDAS_TOTALIZADAS_VENDEDORES]:
    ConstanteEnderecoWebservice.RELATORIO_VENDAS_TOTALIZADAS_VENDEDORES,
};
