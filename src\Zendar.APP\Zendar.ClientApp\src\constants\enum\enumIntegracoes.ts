import { RiQuestionMark } from 'react-icons/ri';

import ConstanteRotas from 'constants/rotas';

import {
  IconDashboardGerencial,
  IconSmartPosApp,
  IconTrayApp,
  IconTrayAppWhite,
  IconMercadoLivreApp,
  IconNuvemIntegracaoApp,
  IconShopeeApp,
  IconSmartPosAppAtivo,
  LogoZoop,
  IconPdvOffline,
  LogoFrenteCaixa,
  IconFrenteCaixaDetalhesIntegracao,
  LogoCardapioDigital,
} from 'icons';

import { enumReferenciaServicoStargate } from './referenciaServicoStargate';

export const enumIntegracoes = {
  options: [
    {
      proximoLancamento: false,
      id: 'dashboard',
      ativo: true,
      bg: '#c2fc1c',
      name: 'Dashboard',
      icon: IconDashboardGerencial,
      iconInativo: IconDashboardGerencial,
      rotaDetalhesIntegracao: ConstanteRotas.DASHBOARD_GERENCIAL_DETALHES,
      rotaComercial: ConstanteRotas.DASHBOARD_GERENCIAL,
      size: '180px',
    },
    {
      proximoLancamento: false,
      id: 'smartPos',
      ativo: true,
      bg: 'primary.500',
      name: 'Smart Pos',
      icon: IconSmartPosAppAtivo,
      iconInativo: IconSmartPosApp,
      rotaDetalhesIntegracao: ConstanteRotas.SMART_POS_DETALHES_INTEGRACAO,
      referenciaServico: enumReferenciaServicoStargate.DISPOSITIVO_SMART_POS,
      rotaComercial: ConstanteRotas.SMART_POS,
      size: '160px',
    },
    {
      proximoLancamento: false,
      id: 'tray',
      ativo: true,
      bg: '#182250',
      name: 'tray',
      icon: IconTrayAppWhite,
      referenciaServico: enumReferenciaServicoStargate.INTEGRACAO_TRAY,
      iconInativo: IconTrayApp,
      rotaDetalhesIntegracao: ConstanteRotas.INTEGRACAO_TRAY_DETALHES,
      rotaComercial: ConstanteRotas.INTEGRACAO_TRAY_TELA_COMERCIAL,
      size: '120px',
    },
    {
      proximoLancamento: false,
      id: 'zoop',
      ativo: true,
      bg: '#EE741C',
      name: 'Zoop Recebimentos',
      icon: LogoZoop,
      referenciaServico: enumReferenciaServicoStargate.INTEGRACAO_ZOOP,
      iconInativo: LogoZoop,
      rotaComercial: ConstanteRotas.ZOOP_TELA_COMERCIAL,
      rotaDetalhesIntegracao: ConstanteRotas.ZOOP_DETALHES_INTEGRACAO,
      size: '150px',
    },
    {
      proximoLancamento: false,
      id: 'pdv-offline',
      ativo: true,
      bg: 'primary.900',
      name: 'Pdv Offline',
      icon: IconPdvOffline,
      referenciaServico: enumReferenciaServicoStargate.DISPOSITIVO_PDV,
      iconInativo: IconPdvOffline,
      rotaComercial: ConstanteRotas.PDV_OFFLINE_TELA_COMERCIAL,
      rotaDetalhesIntegracao: ConstanteRotas.PDV_OFFLINE_DETALHES,
      size: '160px',
    },
    {
      proximoLancamento: false,
      id: 'frente-caixa',
      ativo: true,
      bg: '#FF005A',
      name: 'Frente de Caixa',
      icon: LogoFrenteCaixa,
      referenciaServico: [
        enumReferenciaServicoStargate.DISPOSITIVO_FRENTE_CAIXA,
        enumReferenciaServicoStargate.MODULO_FRENTE_CAIXA,
      ],
      iconInativo: IconFrenteCaixaDetalhesIntegracao,
      rotaDetalhesIntegracao: ConstanteRotas.FRENTE_CAIXA_DETALHES,
      rotaComercial: ConstanteRotas.FRENTE_CAIXA_COMERCIAL,
      size: '200px',
    },
    {
      proximoLancamento: false,
      id: 'cardapio',
      ativo: true,
      bg: '#581F00',
      name: 'Cardápio',
      icon: LogoCardapioDigital,
      iconInativo: LogoCardapioDigital,
      rotaDetalhesIntegracao: ConstanteRotas.CARDAPIO_DETALHES_INTEGRACAO,
      referenciaServico: enumReferenciaServicoStargate.INTEGRACAO_CARDAPIO,
      rotaComercial: ConstanteRotas.CARDAPIO,
      size: '190px',
    },
    {
      proximoLancamento: false,
      id: 'nuvemShop',
      ativo: false,
      bg: 'yellow.600',
      name: 'Nuvem shop',
      icon: IconNuvemIntegracaoApp,
      rotaDetalhesIntegracao: '',
      iconInativo: IconNuvemIntegracaoApp,
      rotaComercial: '',
      identificacaoAplicativo: 0,
      size: '160px',
    },
    {
      proximoLancamento: false,
      id: 'mercadoLivre',
      ativo: false,
      bg: 'Black',
      name: 'Mercado livre',
      icon: IconMercadoLivreApp,
      rotaDetalhesIntegracao: '',
      iconInativo: IconMercadoLivreApp,
      rotaComercial: '',
      size: '160px',
    },

    {
      proximoLancamento: false,
      id: 'shopee',
      ativo: false,
      bg: 'yellow.600',
      name: 'Shopee',
      icon: IconShopeeApp,
      rotaDetalhesIntegracao: '',
      iconInativo: IconShopeeApp,
      rotaComercial: '',
      size: '160px',
    },
  ],
};
